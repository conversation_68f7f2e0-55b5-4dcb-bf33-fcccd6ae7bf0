import SwiftUI

// MARK: - Tora Logo Components
struct ToraLogo: View {
    let size: CGFloat

    init(size: CGFloat = 100) {
        self.size = size
    }
    var body: some View {
        Image("ToraLogo")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: size, height: size * (109.34 / 357.41)) // Maintain SVG aspect ratio
    }
}

// MARK: - Legacy Shape (for backward compatibility if needed)
struct ToraIcon: Shape {
    func path(in rect: CGRect) -> Path {
        // Simplified placeholder - use ToraLogo view instead
        var path = Path()
        path.addRect(rect)
        return path
    }
}

// MARK: - Icon Extensions
extension Image {
    static func toraLogo(size: CGFloat = 100) -> some View {
        ToraLogo(size: size)
    }
}
